import os
from pathlib import Path

# Check the dataset paths
train_images_path = r'C:\Users\<USER>\Downloads\final_road.v1i.yolov12\train\images'
train_labels_path = r'C:\Users\<USER>\Downloads\final_road.v1i.yolov12\train\labels'

print(f"Train images path exists: {os.path.exists(train_images_path)}")
print(f"Train labels path exists: {os.path.exists(train_labels_path)}")

# Check what files are found
images_dir = Path(train_images_path)
image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png"))
print(f"Number of image files found: {len(image_files)}")

if len(image_files) > 0:
    print(f"First few image files:")
    for i, img_file in enumerate(image_files[:5]):
        print(f"  {img_file}")
        # Check corresponding label
        label_path = Path(train_labels_path) / f"{img_file.stem}.txt"
        print(f"    Label exists: {label_path.exists()}")

# Test the dataset class
from train import RoadDataset

try:
    dataset = RoadDataset(train_images_path, train_labels_path)
    print(f"Dataset length: {len(dataset)}")
    if len(dataset) > 0:
        print("Dataset created successfully!")
        # Try to get first item
        img, mask = dataset[0]
        print(f"First item - Image shape: {img.shape}, Mask shape: {mask.shape}")
    else:
        print("Dataset is empty!")
except Exception as e:
    print(f"Error creating dataset: {e}")
