import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from pathlib import Path
import cv2
import numpy as np
from torch.utils.data import Dataset
import albumentations as A
from albumentations.pytorch import ToTensorV2

# Import from train.py
from train import RoadDataset, UNet

def test_training():
    print("Starting test training...")
    
    # Load config manually
    config = {
        'train': r'C:\Users\<USER>\Downloads\final_road.v1i.yolov12\train\images',
        'val': r'C:\Users\<USER>\Downloads\final_road.v1i.yolov12\valid\images'
    }
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Transforms
    train_transform = A.Compose([
        A.Resize(256, 256),  # Smaller size for faster testing
        A.Normalize(),
        ToTensorV2()
    ])
    
    print("Creating datasets...")
    # Datasets
    train_dataset = RoadDataset(
        config['train'],
        config['train'].replace('images', 'labels'),
        train_transform
    )
    
    print(f"Train dataset size: {len(train_dataset)}")
    
    # Test loading a single sample
    print("Testing single sample...")
    sample_img, sample_mask = train_dataset[0]
    print(f"Sample image shape: {sample_img.shape}")
    print(f"Sample mask shape: {sample_mask.shape}")
    print(f"Sample mask unique values: {torch.unique(sample_mask)}")
    
    # Create a small dataloader for testing
    train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True)
    print(f"DataLoader created with {len(train_loader)} batches")
    
    # Model
    print("Creating model...")
    model = UNet(n_classes=2).to(device)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=1e-4)
    
    # Test one batch
    print("Testing one batch...")
    model.train()
    
    for i, (images, masks) in enumerate(train_loader):
        print(f"Batch {i+1}: Images shape: {images.shape}, Masks shape: {masks.shape}")
        
        images, masks = images.to(device), masks.to(device)
        
        print("Forward pass...")
        optimizer.zero_grad()
        outputs = model(images)
        print(f"Model output shape: {outputs.shape}")
        
        print("Computing loss...")
        loss = criterion(outputs, masks)
        print(f"Loss: {loss.item()}")
        
        print("Backward pass...")
        loss.backward()
        optimizer.step()
        
        print(f"Batch {i+1} completed successfully!")
        
        if i >= 2:  # Test only first 3 batches
            break
    
    print("Test training completed successfully!")

if __name__ == "__main__":
    test_training()
